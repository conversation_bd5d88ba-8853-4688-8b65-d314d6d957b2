# AWS Lambda Container Deployment Script for IBE-PreProd-PDF-Splitting-Processor

# Configuration
$AWS_REGION = "us-east-1"
$ECR_REPOSITORY_NAME = "ocr-preprod-split-lambda"
$IMAGE_TAG = "latest"
$LAMBDA_FUNCTION_NAME = "IBE-PreProd-PDF-Splitting-Processor"
$LAMBDA_LAYER_ARN = "arn:aws:lambda:us-east-1:************:layer:attachment-processing-layer:67"

# AWS Credentials
$env:AWS_ACCESS_KEY_ID = "********************"
$env:AWS_SECRET_ACCESS_KEY = "5j2hMBZHl6QpOKAJeSqElD0QTPm4NdG/bFlggO7k"
$env:AWS_DEFAULT_REGION = $AWS_REGION

Write-Host "Starting Lambda container deployment..." -ForegroundColor Green

# Get AWS account ID
$AWS_ACCOUNT_ID = "************"
$ECR_REPOSITORY_URI = "${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/${ECR_REPOSITORY_NAME}"

Write-Host "Configuration:" -ForegroundColor Yellow
Write-Host "  AWS Region: $AWS_REGION"
Write-Host "  ECR Repository: $ECR_REPOSITORY_NAME"
Write-Host "  Image Tag: $IMAGE_TAG"
Write-Host "  ECR URI: $ECR_REPOSITORY_URI"
Write-Host "  Lambda Function: $LAMBDA_FUNCTION_NAME"
Write-Host "  Lambda Layer ARN: $LAMBDA_LAYER_ARN"

# Check if Docker is available
try {
    docker --version | Out-Null
    Write-Host "Docker is available" -ForegroundColor Green
} catch {
    Write-Host "Docker is not available. Please install Docker Desktop first." -ForegroundColor Red
    exit 1
}

# Check if AWS CLI is available
try {
    aws --version | Out-Null
    Write-Host "AWS CLI is available" -ForegroundColor Green
} catch {
    Write-Host "AWS CLI is not available. Please install AWS CLI first." -ForegroundColor Red
    exit 1
}

# Create ECR repository if it doesn't exist
Write-Host "Creating ECR repository if it doesn't exist..." -ForegroundColor Yellow
aws ecr describe-repositories --repository-names $ECR_REPOSITORY_NAME --region $AWS_REGION 2>$null
if ($LASTEXITCODE -ne 0) {
    aws ecr create-repository --repository-name $ECR_REPOSITORY_NAME --region $AWS_REGION
    Write-Host "ECR repository created" -ForegroundColor Green
} else {
    Write-Host "ECR repository already exists" -ForegroundColor Green
}

# Get ECR login token
Write-Host "Logging in to ECR..." -ForegroundColor Yellow
aws ecr get-login-password --region $AWS_REGION | docker login --username AWS --password-stdin $ECR_REPOSITORY_URI

# Build the Docker image
Write-Host "Building Docker image..." -ForegroundColor Yellow
docker build -t "${ECR_REPOSITORY_NAME}:${IMAGE_TAG}" .

# Tag the image for ECR
Write-Host "Tagging image for ECR..." -ForegroundColor Yellow
docker tag "${ECR_REPOSITORY_NAME}:${IMAGE_TAG}" "${ECR_REPOSITORY_URI}:${IMAGE_TAG}"

# Push the image to ECR
Write-Host "Pushing image to ECR..." -ForegroundColor Yellow
docker push "${ECR_REPOSITORY_URI}:${IMAGE_TAG}"

Write-Host "Deployment completed successfully!" -ForegroundColor Green
Write-Host ""
Write-Host "Next steps:" -ForegroundColor Yellow
Write-Host "1. Create or update your Lambda function '$LAMBDA_FUNCTION_NAME' with:" -ForegroundColor White
Write-Host "   Image URI: ${ECR_REPOSITORY_URI}:${IMAGE_TAG}" -ForegroundColor Cyan
Write-Host ""
Write-Host "2. Add the attachment processor layer:" -ForegroundColor White
Write-Host "   Layer ARN: $LAMBDA_LAYER_ARN" -ForegroundColor Cyan
Write-Host ""
Write-Host "3. Configure Lambda function settings:" -ForegroundColor White
Write-Host "   - Memory: 2048 MB (recommended for PDF processing)" -ForegroundColor Cyan
Write-Host "   - Timeout: 300 seconds (5 minutes)" -ForegroundColor Cyan
Write-Host "   - Environment variables: Set your AWS configuration" -ForegroundColor Cyan
Write-Host ""
Write-Host "4. Test your function with the provided test event in lambda_function.py" -ForegroundColor White 