# Create Lambda Function Script for IBE-PreProd-PDF-Splitting-Processor

# Configuration
$AWS_REGION = "us-east-1"
$LAMBDA_FUNCTION_NAME = "IBE-PreProd-PDF-Splitting-Processor"
$LAMBDA_LAYER_ARN = "arn:aws:lambda:us-east-1:953265903196:layer:attachment-processing-layer:67"
$ECR_REPOSITORY_URI = "953265903196.dkr.ecr.us-east-1.amazonaws.com/ocr-preprod-split-lambda:latest"

# AWS Credentials
$env:AWS_ACCESS_KEY_ID = "********************"
$env:AWS_SECRET_ACCESS_KEY = "5j2hMBZHl6QpOKAJeSqElD0QTPm4NdG/bFlggO7k"
$env:AWS_DEFAULT_REGION = $AWS_REGION

Write-Host "Creating Lambda function..." -ForegroundColor Green

# Check if function already exists
Write-Host "Checking if function already exists..." -ForegroundColor Yellow
$existingFunction = aws lambda get-function --function-name $LAMBDA_FUNCTION_NAME --region $AWS_REGION 2>$null

if ($LASTEXITCODE -eq 0) {
    Write-Host "Function already exists. Updating..." -ForegroundColor Yellow
    
    # Update function code
    aws lambda update-function-code `
        --function-name $LAMBDA_FUNCTION_NAME `
        --image-uri $ECR_REPOSITORY_URI `
        --region $AWS_REGION
    
    # Update function configuration
    aws lambda update-function-configuration `
        --function-name $LAMBDA_FUNCTION_NAME `
        --timeout 300 `
        --memory-size 2048 `
        --region $AWS_REGION
    
    Write-Host "Function updated successfully!" -ForegroundColor Green
} else {
    Write-Host "Creating new function..." -ForegroundColor Yellow
    
    # Create function
    aws lambda create-function `
        --function-name $LAMBDA_FUNCTION_NAME `
        --package-type Image `
        --code ImageUri=$ECR_REPOSITORY_URI `
        --role arn:aws:iam::953265903196:role/lambda-execution-role `
        --timeout 300 `
        --memory-size 2048 `
        --region $AWS_REGION
    
    Write-Host "Function created successfully!" -ForegroundColor Green
}

# Add layer to function
Write-Host "Adding layer to function..." -ForegroundColor Yellow
aws lambda update-function-configuration `
    --function-name $LAMBDA_FUNCTION_NAME `
    --layers $LAMBDA_LAYER_ARN `
    --region $AWS_REGION

Write-Host "Layer added successfully!" -ForegroundColor Green

# Set environment variables
Write-Host "Setting environment variables..." -ForegroundColor Yellow
$envVars = @{
    "DEV_BASE_URL" = "https://dev-api.ibuyefficient.com"
    "SQA_BASE_URL" = "https://sqa-api.ibuyefficient.com"
    "PROD_BASE_URL" = "https://api.ibuyefficient.com"
    "SQS_QUEUE_URL" = "https://sqs.us-east-1.amazonaws.com/953265903196/ibe-preprod-queue"
    "SOURCE_EMAIL_ID" = "<EMAIL>"
    "INVALID_FORMAT_SUBJECT" = "Invalid File Format"
    "INVALID_FORMAT_BODY" = "The file {attachment_name} has an invalid format."
    "INVALID_SIZE_SUBJECT" = "File Size Exceeds Limit"
    "INVALID_SIZE_BODY" = "The file {attachment_name} exceeds the maximum size of {max_file_size_mb} MB."
    "PDF_ONLY_SUBJECT" = "PDF Only Files Accepted"
    "PDF_ONLY_BODY" = "Only PDF files are accepted. The file {attachment_name} is not a PDF."
    "FILE_EXTENSION_CONTENT_MISMATCH_SUBJECT" = "File Content Mismatch"
    "FILE_EXTENSION_CONTENT_MISMATCH_BODY" = "The file {attachment_name} has a content mismatch with its extension."
    "MAX_FILE_SIZE_LIMIT_MB" = "50"
    "API_NUMBER_OF_RETRY_ATTEMPT" = "3"
    "API_WAIT_EXPONENTIAL_MULTIPLIER" = "2"
    "API_WAIT_EXPONENTIAL_MAX" = "10"
    "GET_COMPANY_ATTRIBUTES_API_PATH" = "/api/company/{company_id}/attributes"
    "OCR_ATTACH_API_PATH" = "/api/ocr/attach"
    "SUBMIT_FOR_OCR_API_PATH" = "/api/ocr/submit"
    "INFECTED_ATTACHMENT_FILE_NAME_EMAIL" = "infected_email_{recipient_email}_{time_stamp}.txt"
    "INFECTED_ATTACHMENT_FILE_NAME_SFTP" = "infected_sftp_{time_stamp}.txt"
}

# Convert to AWS CLI format
$envString = ""
foreach ($key in $envVars.Keys) {
    $envString += "$key=$($envVars[$key]),"
}
$envString = $envString.TrimEnd(',')

aws lambda update-function-configuration `
    --function-name $LAMBDA_FUNCTION_NAME `
    --environment Variables={$envString} `
    --region $AWS_REGION

Write-Host "Environment variables set successfully!" -ForegroundColor Green

Write-Host ""
Write-Host "Lambda function setup completed!" -ForegroundColor Green
Write-Host "Function Name: $LAMBDA_FUNCTION_NAME" -ForegroundColor Cyan
Write-Host "Image URI: $ECR_REPOSITORY_URI" -ForegroundColor Cyan
Write-Host "Layer ARN: $LAMBDA_LAYER_ARN" -ForegroundColor Cyan
Write-Host ""
Write-Host "Next steps:" -ForegroundColor Yellow
Write-Host "1. Test the function with the provided test event" -ForegroundColor White
Write-Host "2. Monitor CloudWatch logs for execution details" -ForegroundColor White
Write-Host "3. Set up triggers (S3, SQS, etc.) as needed" -ForegroundColor White 