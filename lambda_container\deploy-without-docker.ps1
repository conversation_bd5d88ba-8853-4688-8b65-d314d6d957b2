# Deployment Script for IBE-PreProd-PDF-Splitting-Processor (Without Docker)
# This script sets up IAM role and Lambda function configuration

# Configuration
$AWS_REGION = "us-east-1"
$AWS_ACCOUNT_ID = "************"
$ECR_REPOSITORY_NAME = "ocr-preprod-split-lambda"
$IMAGE_TAG = "latest"
$LAMBDA_FUNCTION_NAME = "IBE-PreProd-PDF-Splitting-Processor"
$LAMBDA_LAYER_ARN = "arn:aws:lambda:us-east-1:************:layer:attachment-processing-layer:67"
$IAM_ROLE_NAME = "lambda-execution-role"
$ECR_REPOSITORY_URI = "${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/${ECR_REPOSITORY_NAME}"

# AWS Credentials
$env:AWS_ACCESS_KEY_ID = "********************"
$env:AWS_SECRET_ACCESS_KEY = "5j2hMBZHl6QpOKAJeSqElD0QTPm4NdG/bFlggO7k"
$env:AWS_DEFAULT_REGION = $AWS_REGION

Write-Host "Starting Deployment Setup for IBE-PreProd-PDF-Splitting-Processor" -ForegroundColor Green
Write-Host "==================================================================" -ForegroundColor Green

# Step 1: Check AWS CLI
Write-Host "Step 1: Checking AWS CLI..." -ForegroundColor Yellow

try {
    $awsVersion = aws --version 2>$null
    if ($awsVersion) {
        Write-Host "AWS CLI is available: $awsVersion" -ForegroundColor Green
    } else {
        throw "AWS CLI not found"
    }
} catch {
    Write-Host "AWS CLI is not available. Please install AWS CLI first." -ForegroundColor Red
    Write-Host "Download from: https://aws.amazon.com/cli/" -ForegroundColor Cyan
    exit 1
}

# Step 2: Create IAM Role
Write-Host "Step 2: Creating IAM Role..." -ForegroundColor Yellow

# Check if role exists
$roleExists = aws iam get-role --role-name $IAM_ROLE_NAME --region $AWS_REGION 2>$null
if ($LASTEXITCODE -eq 0) {
    Write-Host "IAM Role '$IAM_ROLE_NAME' already exists" -ForegroundColor Green
} else {
    Write-Host "Creating IAM Role '$IAM_ROLE_NAME'..." -ForegroundColor Yellow
    
    # Create trust policy
    $trustPolicy = @"
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Principal": {
                "Service": "lambda.amazonaws.com"
            },
            "Action": "sts:AssumeRole"
        }
    ]
}
"@
    
    $trustPolicy | Out-File -FilePath "trust-policy.json" -Encoding UTF8
    
    # Create role
    aws iam create-role --role-name $IAM_ROLE_NAME --assume-role-policy-document file://trust-policy.json --region $AWS_REGION
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "IAM Role created successfully" -ForegroundColor Green
        
        # Attach policies
        Write-Host "Attaching policies to IAM Role..." -ForegroundColor Yellow
        aws iam attach-role-policy --role-name $IAM_ROLE_NAME --policy-arn arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole --region $AWS_REGION
        aws iam attach-role-policy --role-name $IAM_ROLE_NAME --policy-arn arn:aws:iam::aws:policy/AmazonS3ReadOnlyAccess --region $AWS_REGION
        aws iam attach-role-policy --role-name $IAM_ROLE_NAME --policy-arn arn:aws:iam::aws:policy/AmazonSQSFullAccess --region $AWS_REGION
        aws iam attach-role-policy --role-name $IAM_ROLE_NAME --policy-arn arn:aws:iam::aws:policy/AmazonSESFullAccess --region $AWS_REGION
        aws iam attach-role-policy --role-name $IAM_ROLE_NAME --policy-arn arn:aws:iam::aws:policy/SecretsManagerReadWrite --region $AWS_REGION
        
        Write-Host "Policies attached successfully" -ForegroundColor Green
    } else {
        Write-Host "Failed to create IAM Role" -ForegroundColor Red
        exit 1
    }
    
    # Clean up
    Remove-Item "trust-policy.json" -ErrorAction SilentlyContinue
}

# Step 3: Create ECR Repository
Write-Host "Step 3: Creating ECR Repository..." -ForegroundColor Yellow

aws ecr describe-repositories --repository-names $ECR_REPOSITORY_NAME --region $AWS_REGION 2>$null
if ($LASTEXITCODE -ne 0) {
    aws ecr create-repository --repository-name $ECR_REPOSITORY_NAME --region $AWS_REGION
    Write-Host "ECR repository created" -ForegroundColor Green
} else {
    Write-Host "ECR repository already exists" -ForegroundColor Green
}

# Step 4: Check if Lambda Function exists
Write-Host "Step 4: Checking Lambda Function..." -ForegroundColor Yellow

$existingFunction = aws lambda get-function --function-name $LAMBDA_FUNCTION_NAME --region $AWS_REGION 2>$null
if ($LASTEXITCODE -eq 0) {
    Write-Host "Lambda function already exists" -ForegroundColor Green
    
    # Update function configuration
    Write-Host "Updating function configuration..." -ForegroundColor Yellow
    aws lambda update-function-configuration --function-name $LAMBDA_FUNCTION_NAME --timeout 300 --memory-size 2048 --region $AWS_REGION
    
    # Add layer
    Write-Host "Adding Lambda layer..." -ForegroundColor Yellow
    aws lambda update-function-configuration --function-name $LAMBDA_FUNCTION_NAME --layers $LAMBDA_LAYER_ARN --region $AWS_REGION
    
    Write-Host "Function updated successfully!" -ForegroundColor Green
} else {
    Write-Host "Lambda function does not exist yet" -ForegroundColor Yellow
    Write-Host "You need to deploy the container image first using Docker" -ForegroundColor Red
    Write-Host "After deploying the container, run this script again to complete the setup" -ForegroundColor Yellow
}

# Step 5: Set Environment Variables (if function exists)
if ($LASTEXITCODE -eq 0) {
    Write-Host "Step 5: Setting Environment Variables..." -ForegroundColor Yellow
    
    $envVars = @{
        "DEV_BASE_URL" = "https://dev-api.ibuyefficient.com"
        "SQA_BASE_URL" = "https://sqa-api.ibuyefficient.com"
        "PROD_BASE_URL" = "https://api.ibuyefficient.com"
        "SQS_QUEUE_URL" = "https://sqs.us-east-1.amazonaws.com/************/ibe-preprod-queue"
        "SOURCE_EMAIL_ID" = "<EMAIL>"
        "INVALID_FORMAT_SUBJECT" = "Invalid File Format"
        "INVALID_FORMAT_BODY" = "The file {attachment_name} has an invalid format."
        "INVALID_SIZE_SUBJECT" = "File Size Exceeds Limit"
        "INVALID_SIZE_BODY" = "The file {attachment_name} exceeds the maximum size of {max_file_size_mb} MB."
        "PDF_ONLY_SUBJECT" = "PDF Only Files Accepted"
        "PDF_ONLY_BODY" = "Only PDF files are accepted. The file {attachment_name} is not a PDF."
        "FILE_EXTENSION_CONTENT_MISMATCH_SUBJECT" = "File Content Mismatch"
        "FILE_EXTENSION_CONTENT_MISMATCH_BODY" = "The file {attachment_name} has a content mismatch with its extension."
        "MAX_FILE_SIZE_LIMIT_MB" = "50"
        "API_NUMBER_OF_RETRY_ATTEMPT" = "3"
        "API_WAIT_EXPONENTIAL_MULTIPLIER" = "2"
        "API_WAIT_EXPONENTIAL_MAX" = "10"
        "GET_COMPANY_ATTRIBUTES_API_PATH" = "/api/company/{company_id}/attributes"
        "OCR_ATTACH_API_PATH" = "/api/ocr/attach"
        "SUBMIT_FOR_OCR_API_PATH" = "/api/ocr/submit"
        "INFECTED_ATTACHMENT_FILE_NAME_EMAIL" = "infected_email_{recipient_email}_{time_stamp}.txt"
        "INFECTED_ATTACHMENT_FILE_NAME_SFTP" = "infected_sftp_{time_stamp}.txt"
    }
    
    # Convert to AWS CLI format
    $envString = ""
    foreach ($key in $envVars.Keys) {
        $envString += "$key=$($envVars[$key]),"
    }
    $envString = $envString.TrimEnd(',')
    
    aws lambda update-function-configuration --function-name $LAMBDA_FUNCTION_NAME --environment Variables={$envString} --region $AWS_REGION
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Environment variables set successfully!" -ForegroundColor Green
    } else {
        Write-Host "Failed to set environment variables" -ForegroundColor Red
    }
}

# Final Summary
Write-Host ""
Write-Host "Setup Completed!" -ForegroundColor Green
Write-Host "================" -ForegroundColor Green
Write-Host "IAM Role: arn:aws:iam::${AWS_ACCOUNT_ID}:role/${IAM_ROLE_NAME}" -ForegroundColor Cyan
Write-Host "ECR Repository: ${ECR_REPOSITORY_URI}" -ForegroundColor Cyan
Write-Host "Lambda Function: $LAMBDA_FUNCTION_NAME" -ForegroundColor Cyan
Write-Host "Lambda Layer: $LAMBDA_LAYER_ARN" -ForegroundColor Cyan
Write-Host ""
Write-Host "Next Steps:" -ForegroundColor Yellow
Write-Host "1. Install Docker Desktop from https://www.docker.com/products/docker-desktop/" -ForegroundColor White
Write-Host "2. Run the full deployment script: .\deploy-all.ps1" -ForegroundColor White
Write-Host "3. Or manually deploy the container image using the commands in DEPLOYMENT.md" -ForegroundColor White
Write-Host ""
Write-Host "Useful Links:" -ForegroundColor Yellow
Write-Host "Lambda Console: https://console.aws.amazon.com/lambda/home?region=us-east-1" -ForegroundColor Cyan
Write-Host "ECR Console: https://console.aws.amazon.com/ecr/repositories?region=us-east-1" -ForegroundColor Cyan
Write-Host "IAM Console: https://console.aws.amazon.com/iam/home?region=us-east-1#/roles" -ForegroundColor Cyan 