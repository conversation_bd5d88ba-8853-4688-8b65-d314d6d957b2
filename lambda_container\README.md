# IBE-PreProd-PDF-Splitting-Processor

AWS Lambda container for PDF splitting with attachment processor layer integration.

## Project Structure

```
lambda_container/
├── Dockerfile              # Lambda container configuration
├── lambda_function.py      # Main Lambda function code
├── requirements.txt        # Python dependencies
├── deploy.ps1             # PowerShell deployment script
└── README.md              # This file
```

## Prerequisites

1. **Docker Desktop** - Install from https://www.docker.com/products/docker-desktop/
2. **AWS CLI** - Install from https://aws.amazon.com/cli/
3. **PowerShell** - Should be available on Windows by default

## Configuration

The deployment script is pre-configured with:
- **AWS Account ID**: ************
- **Region**: us-east-1
- **ECR Repository**: ocr-preprod-split-lambda
- **Lambda Function Name**: IBE-PreProd-PDF-Splitting-Processor
- **Lambda Layer ARN**: arn:aws:lambda:us-east-1:************:layer:attachment-processing-layer:67

## Deployment Steps

### 1. Run the Deployment Script

```powershell
# Navigate to the lambda_container directory
cd lambda_container

# Run the deployment script
.\deploy.ps1
```

The script will:
- ✅ Check for Docker and AWS CLI availability
- ✅ Create ECR repository if it doesn't exist
- ✅ Build the Docker image
- ✅ Push the image to ECR
- ✅ Provide next steps for Lambda configuration

### 2. Create/Update Lambda Function

After successful deployment, create or update your Lambda function:

1. **Function Type**: Container image
2. **Image URI**: `************.dkr.ecr.us-east-1.amazonaws.com/ocr-preprod-split-lambda:latest`
3. **Function Name**: `IBE-PreProd-PDF-Splitting-Processor`

### 3. Add Lambda Layer

Add the attachment processor layer to your function:
- **Layer ARN**: `arn:aws:lambda:us-east-1:************:layer:attachment-processing-layer:67`

### 4. Configure Lambda Settings

- **Memory**: 2048 MB (recommended for PDF processing)
- **Timeout**: 300 seconds (5 minutes)
- **Architecture**: x86_64

### 5. Set Environment Variables

Configure these environment variables in your Lambda function:
- `DEV_BASE_URL`
- `SQA_BASE_URL`
- `PROD_BASE_URL`
- `SQS_QUEUE_URL`
- `SOURCE_EMAIL_ID`
- `INVALID_FORMAT_SUBJECT`
- `INVALID_FORMAT_BODY`
- `INVALID_SIZE_SUBJECT`
- `INVALID_SIZE_BODY`
- `PDF_ONLY_SUBJECT`
- `PDF_ONLY_BODY`
- `FILE_EXTENSION_CONTENT_MISMATCH_SUBJECT`
- `FILE_EXTENSION_CONTENT_MISMATCH_BODY`
- `MAX_FILE_SIZE_LIMIT_MB`
- `API_NUMBER_OF_RETRY_ATTEMPT`
- `API_WAIT_EXPONENTIAL_MULTIPLIER`
- `API_WAIT_EXPONENTIAL_MAX`
- `GET_COMPANY_ATTRIBUTES_API_PATH`
- `OCR_ATTACH_API_PATH`
- `SUBMIT_FOR_OCR_API_PATH`
- `INFECTED_ATTACHMENT_FILE_NAME_EMAIL`
- `INFECTED_ATTACHMENT_FILE_NAME_SFTP`

## Testing

Use the test event provided in `lambda_function.py`:

```json
{
    "bucket": "ibe-gd-preprod-attach",
    "key": "DEV/Email/test_company/test-document.pdf",
    "company_id": "TEST_COMPANY",
    "environment": "DEV",
    "original_attachment_id": "test_12345",
    "uses_ocr": true,
    "original_file_name": "test-document.pdf"
}
```

## Function Features

- ✅ PDF splitting based on blank page detection
- ✅ Advanced blank page detection using OpenCV
- ✅ Integration with attachment processor layer
- ✅ S3 file upload and management
- ✅ SQS message handling
- ✅ Error handling and logging

## Troubleshooting

### Common Issues

1. **Docker not found**: Install Docker Desktop
2. **AWS CLI not found**: Install AWS CLI
3. **Permission denied**: Ensure AWS credentials are correct
4. **ECR login failed**: Check AWS credentials and region

### Logs

Check CloudWatch logs for detailed function execution information.

## Security Notes

- AWS credentials are included in the deployment script for convenience
- Consider using IAM roles instead of access keys for production
- Ensure proper IAM permissions for ECR, Lambda, and S3 access 