# ZIP-based deployment without Docker for IBE-PreProd-PDF-Splitting-Processor

# Configuration
$AWS_REGION = "us-east-1"
$LAMBDA_FUNCTION_NAME = "IBE-PreProd-PDF-Splitting-Processor"
$LAMBDA_LAYER_ARN = "arn:aws:lambda:us-east-1:953265903196:layer:attachment-processing-layer:67"

# AWS Credentials
$env:AWS_ACCESS_KEY_ID = "********************"
$env:AWS_SECRET_ACCESS_KEY = "5j2hMBZHl6QpOKAJeSqElD0QTPm4NdG/bFlggO7k"
$env:AWS_DEFAULT_REGION = $AWS_REGION

Write-Host "Starting ZIP-based deployment (no Docker required)..." -ForegroundColor Green
Write-Host "======================================================" -ForegroundColor Green

# Check prerequisites
Write-Host "Checking prerequisites..." -ForegroundColor Yellow

# Check AWS CLI
try {
    $awsVersion = aws --version 2>$null
    Write-Host "✓ AWS CLI: $awsVersion" -ForegroundColor Green
} catch {
    Write-Host "✗ AWS CLI not found. Please install AWS CLI first." -ForegroundColor Red
    exit 1
}

# Check Python
try {
    $pythonVersion = python --version 2>$null
    Write-Host "✓ Python: $pythonVersion" -ForegroundColor Green
} catch {
    Write-Host "✗ Python not found. Please install Python first." -ForegroundColor Red
    exit 1
}

# Create deployment package
Write-Host "Creating deployment package..." -ForegroundColor Yellow

# Create temp directory
$tempDir = "temp_deploy_$(Get-Date -Format 'yyyyMMdd_HHmmss')"
if (Test-Path $tempDir) {
    Remove-Item -Recurse -Force $tempDir
}
New-Item -ItemType Directory -Path $tempDir | Out-Null

try {
    # Copy function code
    Write-Host "Copying function code..." -ForegroundColor Cyan
    Copy-Item "lambda_function.py" -Destination $tempDir
    
    # Copy attachment_processing module if it exists
    if (Test-Path "attachment_processing") {
        Copy-Item -Recurse "attachment_processing" -Destination $tempDir
        Write-Host "✓ Copied attachment_processing module" -ForegroundColor Green
    }

    # Install minimal dependencies (heavy ones should be in the layer)
    Write-Host "Installing minimal dependencies..." -ForegroundColor Cyan
    Set-Location $tempDir
    
    # Create minimal requirements for ZIP deployment
    $minimalReqs = @"
boto3
"@
    $minimalReqs | Out-File -FilePath "requirements.txt" -Encoding UTF8
    
    # Install only boto3 (other heavy packages should be in the layer)
    pip install boto3 -t . --no-deps --quiet
    
    # Remove unnecessary files to reduce package size
    Get-ChildItem -Path . -Recurse -Include "*.pyc", "__pycache__", "*.dist-info" | Remove-Item -Recurse -Force -ErrorAction SilentlyContinue
    
    # Create ZIP package
    Write-Host "Creating ZIP package..." -ForegroundColor Cyan
    $zipPath = "../$LAMBDA_FUNCTION_NAME-deployment.zip"
    
    # Use PowerShell's Compress-Archive
    Compress-Archive -Path * -DestinationPath $zipPath -Force
    
    # Return to original directory
    Set-Location ..
    
    $zipSize = (Get-Item $zipPath).Length / 1MB
    Write-Host "✓ Created deployment package: $([math]::Round($zipSize, 2)) MB" -ForegroundColor Green

    # Deploy to Lambda
    Write-Host "Deploying to AWS Lambda..." -ForegroundColor Yellow
    
    # Check if function exists
    $functionExists = $false
    try {
        aws lambda get-function --function-name $LAMBDA_FUNCTION_NAME --region $AWS_REGION 2>$null | Out-Null
        if ($LASTEXITCODE -eq 0) {
            $functionExists = $true
        }
    } catch {
        $functionExists = $false
    }
    
    if ($functionExists) {
        Write-Host "Function exists. Updating code..." -ForegroundColor Cyan
        aws lambda update-function-code `
            --function-name $LAMBDA_FUNCTION_NAME `
            --zip-file "fileb://$LAMBDA_FUNCTION_NAME-deployment.zip" `
            --region $AWS_REGION
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✓ Function code updated successfully" -ForegroundColor Green
        } else {
            Write-Host "✗ Failed to update function code" -ForegroundColor Red
            exit 1
        }
        
        # Update configuration
        Write-Host "Updating function configuration..." -ForegroundColor Cyan
        aws lambda update-function-configuration `
            --function-name $LAMBDA_FUNCTION_NAME `
            --timeout 300 `
            --memory-size 2048 `
            --region $AWS_REGION
            
    } else {
        Write-Host "Creating new function..." -ForegroundColor Cyan
        aws lambda create-function `
            --function-name $LAMBDA_FUNCTION_NAME `
            --runtime python3.12 `
            --role "arn:aws:iam::953265903196:role/lambda-execution-role" `
            --handler lambda_function.lambda_handler `
            --zip-file "fileb://$LAMBDA_FUNCTION_NAME-deployment.zip" `
            --timeout 300 `
            --memory-size 2048 `
            --region $AWS_REGION
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✓ Function created successfully" -ForegroundColor Green
        } else {
            Write-Host "✗ Failed to create function" -ForegroundColor Red
            exit 1
        }
    }
    
    # Add layer
    Write-Host "Adding attachment processing layer..." -ForegroundColor Cyan
    aws lambda update-function-configuration `
        --function-name $LAMBDA_FUNCTION_NAME `
        --layers $LAMBDA_LAYER_ARN `
        --region $AWS_REGION
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ Layer added successfully" -ForegroundColor Green
    } else {
        Write-Host "⚠ Warning: Failed to add layer (function may still work)" -ForegroundColor Yellow
    }
    
    # Set environment variables
    Write-Host "Setting environment variables..." -ForegroundColor Cyan
    $envVars = "DEV_BASE_URL=https://dev-api.ibuyefficient.com,SQA_BASE_URL=https://sqa-api.ibuyefficient.com,PROD_BASE_URL=https://api.ibuyefficient.com,SQS_QUEUE_URL=https://sqs.us-east-1.amazonaws.com/953265903196/ibe-preprod-queue,SOURCE_EMAIL_ID=<EMAIL>,MAX_FILE_SIZE_LIMIT_MB=50,API_NUMBER_OF_RETRY_ATTEMPT=3"
    
    aws lambda update-function-configuration `
        --function-name $LAMBDA_FUNCTION_NAME `
        --environment "Variables={$envVars}" `
        --region $AWS_REGION
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ Environment variables set successfully" -ForegroundColor Green
    }

} finally {
    # Clean up
    Write-Host "Cleaning up..." -ForegroundColor Yellow
    Set-Location $PSScriptRoot
    if (Test-Path $tempDir) {
        Remove-Item -Recurse -Force $tempDir
    }
    if (Test-Path "$LAMBDA_FUNCTION_NAME-deployment.zip") {
        Remove-Item "$LAMBDA_FUNCTION_NAME-deployment.zip"
    }
}

Write-Host ""
Write-Host "🎉 Deployment completed successfully!" -ForegroundColor Green
Write-Host "======================================" -ForegroundColor Green
Write-Host "Function Name: $LAMBDA_FUNCTION_NAME" -ForegroundColor Cyan
Write-Host "Runtime: python3.12 (ZIP deployment)" -ForegroundColor Cyan
Write-Host "Layer: attachment-processing-layer:67" -ForegroundColor Cyan
Write-Host "Memory: 2048 MB" -ForegroundColor Cyan
Write-Host "Timeout: 300 seconds" -ForegroundColor Cyan
Write-Host ""
Write-Host "Next steps:" -ForegroundColor Yellow
Write-Host "1. Test the function with a sample event" -ForegroundColor White
Write-Host "2. Monitor CloudWatch logs for execution details" -ForegroundColor White
Write-Host "3. Set up triggers (S3, SQS, etc.) as needed" -ForegroundColor White
