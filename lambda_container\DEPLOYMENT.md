# 🚀 Complete Deployment Guide

## Prerequisites

1. **Docker Desktop** - Install from https://www.docker.com/products/docker-desktop/
2. **AWS CLI** - Install from https://aws.amazon.com/cli/
3. **PowerShell** - Should be available on Windows by default

## Step 1: Deploy Container Image

### Option A: Using Deployment Script (Recommended)
```powershell
# Navigate to the lambda_container directory
cd lambda_container

# Run the deployment script
.\deploy.ps1
```

### Option B: Manual Deployment
```powershell
# Set AWS credentials
$env:AWS_ACCESS_KEY_ID = "********************"
$env:AWS_SECRET_ACCESS_KEY = "5j2hMBZHl6QpOKAJeSqElD0QTPm4NdG/bFlggO7k"
$env:AWS_DEFAULT_REGION = "us-east-1"

# Login to ECR
aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin ************.dkr.ecr.us-east-1.amazonaws.com

# Build and push
docker build -t ocr-preprod-split-lambda:latest .
docker tag ocr-preprod-split-lambda:latest ************.dkr.ecr.us-east-1.amazonaws.com/ocr-preprod-split-lambda:latest
docker push ************.dkr.ecr.us-east-1.amazonaws.com/ocr-preprod-split-lambda:latest
```

## Step 2: Create Lambda Function

### Option A: Using Script (Recommended)
```powershell
# Create the Lambda function with all configurations
.\create-lambda.ps1
```

### Option B: Manual Creation via AWS Console

1. **Open AWS Lambda Console**
   - Go to https://console.aws.amazon.com/lambda/
   - Click "Create function"

2. **Function Configuration**
   - **Function name**: `IBE-PreProd-PDF-Splitting-Processor`
   - **Runtime**: Container image
   - **Container image URI**: `************.dkr.ecr.us-east-1.amazonaws.com/ocr-preprod-split-lambda:latest`
   - **Architecture**: x86_64

3. **Function Settings**
   - **Memory**: 2048 MB
   - **Timeout**: 300 seconds (5 minutes)

4. **Add Lambda Layer**
   - Go to "Layers" section
   - Add layer: `arn:aws:lambda:us-east-1:************:layer:attachment-processing-layer:67`

5. **Environment Variables**
   Add these environment variables:
   ```
   DEV_BASE_URL=https://dev-api.ibuyefficient.com
   SQA_BASE_URL=https://sqa-api.ibuyefficient.com
   PROD_BASE_URL=https://api.ibuyefficient.com
   SQS_QUEUE_URL=https://sqs.us-east-1.amazonaws.com/************/ibe-preprod-queue
   SOURCE_EMAIL_ID=<EMAIL>
   INVALID_FORMAT_SUBJECT=Invalid File Format
   INVALID_FORMAT_BODY=The file {attachment_name} has an invalid format.
   INVALID_SIZE_SUBJECT=File Size Exceeds Limit
   INVALID_SIZE_BODY=The file {attachment_name} exceeds the maximum size of {max_file_size_mb} MB.
   PDF_ONLY_SUBJECT=PDF Only Files Accepted
   PDF_ONLY_BODY=Only PDF files are accepted. The file {attachment_name} is not a PDF.
   FILE_EXTENSION_CONTENT_MISMATCH_SUBJECT=File Content Mismatch
   FILE_EXTENSION_CONTENT_MISMATCH_BODY=The file {attachment_name} has a content mismatch with its extension.
   MAX_FILE_SIZE_LIMIT_MB=50
   API_NUMBER_OF_RETRY_ATTEMPT=3
   API_WAIT_EXPONENTIAL_MULTIPLIER=2
   API_WAIT_EXPONENTIAL_MAX=10
   GET_COMPANY_ATTRIBUTES_API_PATH=/api/company/{company_id}/attributes
   OCR_ATTACH_API_PATH=/api/ocr/attach
   SUBMIT_FOR_OCR_API_PATH=/api/ocr/submit
   INFECTED_ATTACHMENT_FILE_NAME_EMAIL=infected_email_{recipient_email}_{time_stamp}.txt
   INFECTED_ATTACHMENT_FILE_NAME_SFTP=infected_sftp_{time_stamp}.txt
   ```

## Step 3: Test the Function

### Option A: Using AWS CLI
```powershell
# Test with the provided test event
aws lambda invoke `
    --function-name IBE-PreProd-PDF-Splitting-Processor `
    --payload file://test-event.json `
    --region us-east-1 `
    response.json

# View the response
Get-Content response.json
```

### Option B: Using AWS Console
1. Go to your Lambda function
2. Click "Test" tab
3. Create new test event with the content from `test-event.json`
4. Click "Test" to execute

## Step 4: Monitor and Verify

### Check CloudWatch Logs
```powershell
# Get the latest log stream
aws logs describe-log-groups --log-group-name-prefix "/aws/lambda/IBE-PreProd-PDF-Splitting-Processor" --region us-east-1

# Get recent log events
aws logs filter-log-events `
    --log-group-name "/aws/lambda/IBE-PreProd-PDF-Splitting-Processor" `
    --start-time $(Get-Date).AddMinutes(-10).ToFileTime() `
    --region us-east-1
```

### Verify Function Configuration
```powershell
# Get function details
aws lambda get-function --function-name IBE-PreProd-PDF-Splitting-Processor --region us-east-1

# Get function configuration
aws lambda get-function-configuration --function-name IBE-PreProd-PDF-Splitting-Processor --region us-east-1
```

## Troubleshooting

### Common Issues

1. **Docker not found**
   - Install Docker Desktop
   - Ensure Docker Desktop is running

2. **ECR login failed**
   - Check AWS credentials
   - Verify region is correct

3. **Function creation failed**
   - Check IAM role exists: `arn:aws:iam::************:role/lambda-execution-role`
   - Verify ECR image exists

4. **Layer not found**
   - Verify layer ARN: `arn:aws:lambda:us-east-1:************:layer:attachment-processing-layer:67`
   - Check layer is in the same region

### IAM Role Requirements

Your Lambda function needs an execution role with these permissions:
- `AWSLambdaBasicExecutionRole`
- `AmazonS3ReadOnlyAccess`
- `AmazonSQSFullAccess`
- `AmazonSESFullAccess`
- `SecretsManagerReadWrite`

## Next Steps

1. **Set up triggers** (S3, SQS, etc.) as needed
2. **Configure monitoring** and alerting
3. **Set up CI/CD** pipeline for automated deployments
4. **Test with real data** in your environment

## Configuration Summary

- **Function Name**: `IBE-PreProd-PDF-Splitting-Processor`
- **Container Image**: `************.dkr.ecr.us-east-1.amazonaws.com/ocr-preprod-split-lambda:latest`
- **Lambda Layer**: `arn:aws:lambda:us-east-1:************:layer:attachment-processing-layer:67`
- **Memory**: 2048 MB
- **Timeout**: 300 seconds
- **Region**: us-east-1 